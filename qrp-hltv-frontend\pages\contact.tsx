import ContactForm from '../components/ContactForm'
import Head from 'next/head'
import { useLanguage } from '../lib/LanguageContext'

export default function ContactPage() {
  const { t } = useLanguage()

  return (
    <>
      <Head>
        <title>{`${t('contact_page_title')} - QRP HLTV`}</title>
        <meta name="description" content="Contact QRP HLTV team - get featured, report issues or ask questions" />
        <meta property="og:title" content={`${t('contact_page_title')} - QRP HLTV`} />
        <meta property="og:description" content="Contact QRP HLTV team - get featured, report issues or ask questions" />
        <meta property="og:url" content="https://qrp-hltv.netlify.app/contact" />
        <meta name="twitter:title" content={`${t('contact_page_title')} - QRP HLTV`} />
        <meta name="twitter:description" content="Contact QRP HLTV team - get featured, report issues or ask questions" />
      </Head>
      <main className="max-w-4xl mx-auto py-12 px-4">
        <div className="flex items-center mb-8">
          <h1 className="text-3xl font-bold mr-4">{t('contact_page_title')}</h1>
          <div className="flex-1 h-1 bg-blue-700 rounded-full opacity-40" />
        </div>

        {/* Simple Contact Message */}
        <div className="mb-8">
          <div className="p-6 rounded-lg bg-zinc-900/50 border border-zinc-800 text-center">
            <p className="text-zinc-300 leading-relaxed text-lg">
              {t('contact_simple_message')}
            </p>
            <div className="mt-4">
              <a
                href="https://discord.gg/tGW2R5eDE6"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center space-x-2 px-4 py-2 bg-[#5865F2] hover:bg-[#4752C4] transition rounded-lg font-semibold text-white"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                </svg>
                <span>Discord</span>
              </a>
            </div>
          </div>
        </div>

        {/* Contact Form */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-zinc-900/50 rounded-lg border border-zinc-800 p-6">
            <h2 className="text-xl font-semibold mb-6 text-blue-400 text-center">{t('contact_form_title')}</h2>
            <ContactForm />
          </div>
        </div>
      </main>
    </>
  )
}
