import { sanity } from '../lib/sanity'
import HighlightsCarousel from '../components/HighlightsCarousel'
import { useLanguage } from '../lib/LanguageContext'
import Head from 'next/head'
import type { SanityImageSource } from '@sanity/image-url/lib/types/types'
import type { PortableTextBlock } from '@portabletext/types'

// Utility function to extract plain text from PortableText
function extractPlainText(portableText: PortableTextBlock[] | string): string {
  if (typeof portableText === 'string') {
    return portableText
  }

  if (!Array.isArray(portableText)) {
    return ''
  }

  return portableText
    .map(block => {
      if (block._type === 'block' && block.children) {
        return block.children
          .map(child => child.text || '')
          .join('')
      }
      return ''
    })
    .join(' ')
    .trim()
}

interface News {
  _id: string;
  title: string;
  summary: PortableTextBlock[] | string;
  publishedAt: string;
  image?: SanityImageSource;
}

// Define proper media asset types for tournaments
interface MediaAsset {
  _type: string;
  asset?: {
    _ref: string;
    _type: string;
    url?: string;
  };
  hotspot?: any;
  crop?: any;
}

interface Tournament {
  _id: string;
  name: string;
  startDate: string;
  endDate: string;
  status: string;
  qualDate: string;
  photo?: MediaAsset;
  video?: MediaAsset;
}

interface CarouselItem {
  _id: string
  title: string
  type: 'news' | 'tournament'
  date: string
  image?: SanityImageSource | null
  video?: {
    _type: string
    asset?: {
      _ref: string
      _type: string
    }
  } | null
  summary?: PortableTextBlock[] | string
  publishedAt?: string
  name?: string
  qualDate?: string
  status?: string
}

export async function getStaticProps() {
  const news = await sanity.fetch(`*[_type == "news" && !(_id in path("drafts.**"))] | order(publishedAt desc)[0...5]{
    _id, title, summary, publishedAt, image
  }`)
  const tournaments = await sanity.fetch(`*[_type == "tournament" && !(_id in path("drafts.**"))] | order(qualDate desc)[0...5]{
    _id, name, startDate, endDate, status, qualDate, photo, video
  }`)

  // Prepare carousel data - combine and sort by date
  const carouselItems: CarouselItem[] = []

  // Add news items
  news.forEach((item: News) => {
    carouselItems.push({
      _id: item._id,
      title: item.title,
      type: 'news',
      date: item.publishedAt,
      image: item.image || null,
      summary: extractPlainText(item.summary),
      publishedAt: item.publishedAt,
    })
  })

  // Add tournament items
  tournaments.forEach((item: Tournament) => {
    // Type guard to check if media item has asset structure
    const hasAssetStructure = (mediaItem: MediaAsset): mediaItem is MediaAsset & { asset: { _ref: string; _type: string } } => {
      return mediaItem.asset !== undefined &&
             mediaItem.asset._ref !== undefined &&
             typeof mediaItem.asset._ref === 'string'
    }

    carouselItems.push({
      _id: item._id,
      title: item.name,
      type: 'tournament',
      date: item.qualDate || item.startDate,
      image: item.photo as SanityImageSource || null,
      video: item.video && hasAssetStructure(item.video) ? {
        _type: 'file',
        asset: {
          _ref: item.video.asset._ref,
          _type: item.video.asset._type
        }
      } : null,
      name: item.name,
      qualDate: item.qualDate,
      status: item.status,
    })
  })

  // Sort by date (most recent first) and take top 5
  const sortedCarouselItems = carouselItems
    .filter(item => item.date) // Filter out items without dates
    .sort((a, b) => {
      const dateA = new Date(a.date).getTime()
      const dateB = new Date(b.date).getTime()
      // Handle invalid dates by putting them at the end
      if (isNaN(dateA) && isNaN(dateB)) return 0
      if (isNaN(dateA)) return 1
      if (isNaN(dateB)) return -1
      return dateB - dateA
    })
    .slice(0, 5)

  return {
    props: { news, tournaments, carouselItems: sortedCarouselItems },
    revalidate: 60, // Revalidate every 60 seconds
  }
}

export default function HomePage({ news, tournaments, carouselItems }: { news: News[], tournaments: Tournament[], carouselItems: CarouselItem[] }) {
  const { t } = useLanguage();
  return (
    <>
      <Head>
        <title>{String(t('home_page_title'))}</title>
        <meta name="description" content="QRP HLTV - CarX Drift Racing tournaments, news, and statistics platform" />
        <meta property="og:title" content={String(t('home_page_title'))} />
        <meta property="og:description" content="QRP HLTV - CarX Drift Racing tournaments, news, and statistics platform" />
        <meta property="og:url" content="https://qrp-hltv.com/" />
        <meta name="twitter:title" content={String(t('home_page_title'))} />
        <meta name="twitter:description" content="QRP HLTV - CarX Drift Racing tournaments, news, and statistics platform" />
      </Head>

      {/* Highlights Carousel */}
      <HighlightsCarousel items={carouselItems} />

      <main className="max-w-4xl mx-auto py-12 px-4 space-y-16">
        {/* News Section */}
        <section>
          <div className="flex items-center mb-6">
            <h2 className="text-2xl font-bold mr-4">{t('latestNews')}</h2>
            <div className="flex-1 h-1 bg-blue-700 rounded-full opacity-40" />
          </div>
          <div className="grid gap-6 md:grid-cols-2">
            {news.map((n: News) => (
              <div key={n._id} className="p-5 rounded-2xl bg-zinc-900 border border-zinc-800 shadow-md hover:shadow-blue-900/40 hover:border-blue-700 transition cursor-pointer group">
                <div className="text-lg font-bold group-hover:text-blue-400 transition">{n.title}</div>
                <div className="text-gray-400 mt-1 mb-2">{extractPlainText(n.summary)}</div>
                <div className="text-xs text-gray-500">{n.publishedAt?.slice(0,10)}</div>
              </div>
            ))}
          </div>
        </section>
        {/* Tournament Section */}
        <section>
          <div className="flex items-center mb-6">
            <h2 className="text-2xl font-bold mr-4">{t('tournaments')}</h2>
            <div className="flex-1 h-1 bg-green-700 rounded-full opacity-40" />
          </div>
          <div className="grid gap-6 md:grid-cols-2">
            {tournaments.map((tour: Tournament) => (
              <div key={tour._id} className="p-5 rounded-2xl bg-zinc-900 border border-zinc-800 shadow-md flex items-center justify-between hover:shadow-green-900/40 hover:border-green-700 transition cursor-pointer group">
                <div>
                  <div className="font-bold text-lg group-hover:text-green-400 transition">{tour.name}</div>
                  <div className="text-gray-400">{tour.startDate?.slice(0,10)}</div>
                  {tour.qualDate && (
                    <div className="text-xs text-gray-500">{t('qualifier')} {tour.qualDate?.slice(0,10)}</div>
                  )}
                </div>
                <span className={`px-2 py-1 rounded text-xs font-bold ${
                  tour.status === "Upcoming" ? "bg-green-700"
                  : "bg-gray-600"
                }`}>
                  {tour.status === "Upcoming" ? t('status_upcoming') : tour.status === "Finished" ? t('status_finished') : tour.status}
                </span>
              </div>
            ))}
          </div>
        </section>
      </main>
    </>
  )
}
