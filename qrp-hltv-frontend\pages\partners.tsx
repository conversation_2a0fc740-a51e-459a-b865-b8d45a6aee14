import { sanity, urlFor } from '../lib/sanity'
import Image from 'next/image'
import Link from 'next/link'
import Head from 'next/head'
import { PortableText } from '@portabletext/react'
import type { PortableTextBlock } from '@portabletext/types'

import { useLanguage } from '../lib/LanguageContext'

interface Partner {
  _id: string;
  name: string;
  logo: any;
  description: PortableTextBlock[] | string;
  website: string;
  order: number;
}

export async function getStaticProps() {
  console.log('PARTNERS.TSX: getStaticProps triggered at ', new Date().toISOString());

  const partners = await sanity.fetch(`*[_type == "partner" && !(_id in path("drafts.**"))] | order(order asc, name asc){
    _id,
    name,
    logo,
    description,
    website,
    order
  }`)
  console.log('PARTNERS.TSX: Fetched partners count:', partners?.length);
  if (partners?.length > 0) {
    console.log('PARTNERS.TSX: First partner fetched:', JSON.stringify(partners[0])); // Log the first partner as an example
  }
  return {
    props: {
      partners
    },
    revalidate: 60, // Revalidate every 60 seconds
  }
}

export default function PartnersPage({ partners }: { partners: Partner[] }) {
  const { t } = useLanguage();
  return (
    <>
      <Head>
        <title>{`${t('partners_page_title')} - QRP HLTV`}</title>
        <meta name="description" content="QRP HLTV partners and sponsors supporting CarX Drift Racing community" />
        <meta property="og:title" content={`${t('partners_page_title')} - QRP HLTV`} />
        <meta property="og:description" content="QRP HLTV partners and sponsors supporting CarX Drift Racing community" />
        <meta property="og:url" content="https://qrp-hltv.netlify.app/partners" />
        <meta name="twitter:title" content={`${t('partners_page_title')} - QRP HLTV`} />
        <meta name="twitter:description" content="QRP HLTV partners and sponsors supporting CarX Drift Racing community" />
      </Head>
      <main className="max-w-4xl mx-auto py-12 px-4">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-yellow-400">{t('partners_page_title')}</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {partners?.map((partner) => (
            <div key={partner._id} className="bg-zinc-900/50 rounded-xl p-6 border border-zinc-800 hover:border-yellow-400/50 transition">
              <div className="flex items-center space-x-4 mb-4">
                {partner.logo && (
                  <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-zinc-800">
                    <Image
                      src={urlFor(partner.logo).url()}
                      alt={partner.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}
                <div>
                  <h2 className="text-xl font-semibold text-yellow-400">{partner.name}</h2>
                  {partner.website && (
                    <a
                      href={partner.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-yellow-400/70 hover:text-yellow-400 transition"
                    >
                      {t('visit_website_button')}
                    </a>
                  )}
                </div>
              </div>
              {partner.description && (
                <div className="text-zinc-300 prose prose-invert prose-sm max-w-none">
                  {typeof partner.description === 'string' ? (
                    <p style={{ whiteSpace: 'pre-wrap' }}>{partner.description}</p>
                  ) : (
                    partner.description.length > 0 && <PortableText value={partner.description} />
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </main>
    </>
  )
}