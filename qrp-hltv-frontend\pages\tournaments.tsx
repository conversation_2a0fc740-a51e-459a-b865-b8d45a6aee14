import { sanity, urlFor } from '../lib/sanity'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { PortableText } from '@portabletext/react'
import Image from 'next/image'
import Head from 'next/head'
import type { PortableTextBlock } from '@portabletext/types'
import { useLanguage } from '../lib/LanguageContext'

interface Tournament {
  _id: string
  name: string
  qualDate: string | null
  tandemsDate: string | null
  status: 'Upcoming' | 'Ongoing' | 'Finished'
  photo?: {
    _type: string
    asset?: {
      _ref: string
      _type: string
      url?: string
    }
    hotspot?: any
    crop?: any
  } | null
  video?: {
    _type: string
    asset?: {
      _ref: string
      _type: string
      url?: string
    }
  } | null
  description: PortableTextBlock[]
}

export async function getStaticProps() {
  const tournaments = await sanity.fetch(`*[_type == "tournament" && !(_id in path("drafts.**"))] | order(qualDate desc){
    _id,
    name,
    "qualDate": qualDate,
    "tandemsDate": tandemsDate,
    status,
    photo,
    video,
    description
  }`)

  const formattedTournaments = tournaments.map((tournament: Tournament) => ({
    ...tournament,
    qualDate: tournament.qualDate ? new Date(tournament.qualDate).toISOString() : null,
    tandemsDate: tournament.tandemsDate ? new Date(tournament.tandemsDate).toISOString() : null,
  }))

  return {
    props: {
      tournaments: formattedTournaments
    },
    revalidate: 60, // Revalidate every 60 seconds
  }
}

export default function TournamentsPage({ tournaments }: { tournaments: Tournament[] }) {
  const router = useRouter()
  const [selectedTournament, setSelectedTournament] = useState<Tournament | null>(null)
  const { t } = useLanguage();

  // Handle query parameter to auto-open tournament modal
  useEffect(() => {
    if (router.isReady && router.query.open && tournaments.length > 0) {
      const tournamentId = router.query.open as string;
      const tournament = tournaments.find(t => t._id === tournamentId);
      if (tournament) {
        setSelectedTournament(tournament);
        // Clean up URL without triggering navigation
        router.replace('/tournaments', undefined, { shallow: true });
      }
    }
  }, [router.isReady, router.query.open, tournaments])

  const getFileAssetUrl = (assetRef: string): string | undefined => {
    if (!assetRef) {
      return undefined;
    }

    const parts = assetRef.split('-');
    if (parts.length !== 3 || parts[0] !== 'file') {
      return undefined;
    }

    const projectId = sanity.config().projectId;
    const dataset = sanity.config().dataset;
    const assetId = parts[1];
    const extension = parts[2];

    if (!projectId || !dataset || !assetId || !extension) {
      return undefined;
    }

    return `https://cdn.sanity.io/files/${projectId}/${dataset}/${assetId}.${extension}`;
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return t('date_tba');
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return t('date_tba');

      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'UTC'
      })
    } catch (error) {
      console.error('Error formatting date:', error)
      return t('date_tba');
    }
  }

  return (
    <>
      <Head>
        <title>{`${t('tournaments_page_title')} - QRP HLTV`}</title>
        <meta name="description" content="CarX Drift Racing tournaments - upcoming, ongoing and finished competitions" />
        <meta property="og:title" content={`${t('tournaments_page_title')} - QRP HLTV`} />
        <meta property="og:description" content="CarX Drift Racing tournaments - upcoming, ongoing and finished competitions" />
        <meta property="og:url" content="https://qrp-hltv.netlify.app/tournaments" />
        <meta name="twitter:title" content={`${t('tournaments_page_title')} - QRP HLTV`} />
        <meta name="twitter:description" content="CarX Drift Racing tournaments - upcoming, ongoing and finished competitions" />
      </Head>
      <main className="max-w-4xl mx-auto py-12 px-4">
        <div className="flex items-center mb-8">
          <h1 className="text-3xl font-bold mr-4">{t('tournaments_page_title')}</h1>
          <div className="flex-1 h-1 bg-green-700 rounded-full opacity-40" />
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          {tournaments.map((tour: Tournament) => (
            <div
              key={tour._id}
              className="p-5 rounded-2xl bg-zinc-900 border border-zinc-800 shadow-md hover:shadow-green-900/40 hover:border-green-700 transition cursor-pointer group" // Removed 'flex flex-col'
              onClick={() => setSelectedTournament(tour)}
            >
              <div className="flex items-start space-x-4"> {/* Main flex container for text + image */}
                <div className="flex-1 flex flex-col"> {/* Container for all text elements */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="font-bold text-lg group-hover:text-green-400 transition">{tour.name}</div>
                    <span className={`px-2 py-1 rounded text-xs font-bold ${
                      tour.status === "Upcoming" ? "bg-green-700" :
                      tour.status === "Ongoing" ? "bg-blue-700" : "bg-gray-600"
                    }`}>
                      {tour.status === "Upcoming" ? t('status_upcoming') : tour.status === "Ongoing" ? t('status_ongoing') : tour.status === "Finished" ? t('status_finished') : tour.status}
                    </span>
                  </div>
                  <div className="text-gray-400 space-y-1">
                    <div>{t('qualifiers_label')} {formatDate(tour.qualDate)}</div>
                    <div>{t('tandems_label')} {formatDate(tour.tandemsDate)}</div>
                  </div>
                </div>
                {tour.photo && tour.photo.asset && (
                  <div className="w-36 h-24 flex-shrink-0"> {/* Container for the image */}
                    <Image
                      src={urlFor(tour.photo).width(150).height(100).url()}
                      alt={tour.name || 'Tournament image'}
                      width={150}
                      height={100}
                      className="rounded-md object-cover w-full h-full"
                    />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Tournament Modal */}
        {selectedTournament && (
          <div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedTournament(null)}
          >
            <div
              className="bg-zinc-900 rounded-2xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-zinc-700"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-start mb-4">
                <h2 className="text-2xl font-bold text-green-400">{selectedTournament.name}</h2>
                <button
                  onClick={() => setSelectedTournament(null)}
                  className="text-zinc-400 hover:text-zinc-100 transition"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <span className={`px-3 py-1 rounded text-sm font-bold ${
                      selectedTournament.status === "Upcoming" ? "bg-green-700" :
                      selectedTournament.status === "Ongoing" ? "bg-blue-700" : "bg-gray-600"
                    }`}>
                      {selectedTournament.status === "Upcoming" ? t('status_upcoming') : selectedTournament.status === "Ongoing" ? t('status_ongoing') : selectedTournament.status === "Finished" ? t('status_finished') : t(selectedTournament.status)}
                    </span>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-zinc-400">{t('qualifiers_label')}</span>
                      <span className="font-medium">{formatDate(selectedTournament.qualDate)}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-zinc-400">{t('tandems_label')}</span>
                      <span className="font-medium">{formatDate(selectedTournament.tandemsDate)}</span>
                    </div>
                  </div>

                  {selectedTournament.description && (
                    <div className="mt-6 prose prose-invert max-w-none">
                      <h3 className="text-lg font-semibold mb-2">{t('description_label')}</h3>
                      <PortableText value={selectedTournament.description} />
                    </div>
                  )}
                </div>

                <div className="md:ml-auto flex items-start justify-center md:justify-end">
                  {(selectedTournament.photo || selectedTournament.video) && (
                    <div className="mt-0 md:mt-0 space-y-4">
                      {selectedTournament.photo && selectedTournament.photo.asset && (
                        <img
                          src={urlFor(selectedTournament.photo).width(400).url()}
                          alt={selectedTournament.name}
                          className="rounded-lg shadow-lg object-contain max-h-80"
                        />
                      )}
                      {selectedTournament.video && selectedTournament.video.asset && (
                        <video
                          src={getFileAssetUrl(selectedTournament.video.asset._ref)}
                          controls
                          preload="metadata"
                          className="rounded-lg shadow-lg max-h-80 w-full"
                          onError={(e) => {
                            console.error('Tournament video failed to load:', e)
                            if (selectedTournament.video?.asset?._ref) {
                              console.error('Video URL:', getFileAssetUrl(selectedTournament.video.asset._ref))
                            }
                          }}
                          onLoadStart={() => {
                            console.log('Tournament video loading started')
                          }}
                        >
                          Your browser does not support the video tag.
                        </video>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </>
  )
}