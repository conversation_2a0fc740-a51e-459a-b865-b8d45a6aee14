import { defineCliConfig } from 'sanity/cli'

const projectId = process.env.SANITY_PROJECT_ID || 'al3wd5y8'
const dataset = process.env.SANITY_DATASET || 'production'

if (!projectId) {
  throw new Error('SANITY_PROJECT_ID is required')
}

if (!dataset) {
  throw new Error('SANITY_DATASET is required')
}

export default defineCliConfig({
  api: {
    projectId,
    dataset
  },
  studioHost: 'qrp-hltv'
})
